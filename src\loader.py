"""Document loader module for loading text files from the data folder."""

import os
from typing import List, Dict
from pathlib import Path
import config


class Document:
    """Simple document class to hold text content and metadata."""
    
    def __init__(self, content: str, metadata: Dict[str, str] = None):
        self.content = content
        self.metadata = metadata or {}
    
    def __repr__(self):
        return f"Document(content_length={len(self.content)}, metadata={self.metadata})"


class DocumentLoader:
    """Loads documents from the data folder."""
    
    def __init__(self, data_folder: str = None):
        self.data_folder = Path(data_folder or config.DATA_FOLDER)
        self.supported_extensions = config.SUPPORTED_EXTENSIONS
    
    def load_documents(self) -> List[Document]:
        """Load all supported documents from the data folder."""
        documents = []
        
        if not self.data_folder.exists():
            print(f"Warning: Data folder '{self.data_folder}' does not exist.")
            return documents
        
        for file_path in self.data_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                try:
                    document = self._load_file(file_path)
                    documents.append(document)
                    print(f"Loaded: {file_path.name}")
                except Exception as e:
                    print(f"Error loading {file_path.name}: {e}")
        
        print(f"Total documents loaded: {len(documents)}")
        return documents
    
    def _load_file(self, file_path: Path) -> Document:
        """Load a single text file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        metadata = {
            'filename': file_path.name,
            'filepath': str(file_path),
            'size': len(content)
        }
        
        return Document(content=content, metadata=metadata)
    
    def load_single_document(self, filename: str) -> Document:
        """Load a single document by filename."""
        file_path = self.data_folder / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"File '{filename}' not found in {self.data_folder}")
        
        if file_path.suffix.lower() not in self.supported_extensions:
            raise ValueError(f"File extension '{file_path.suffix}' not supported")
        
        return self._load_file(file_path)


if __name__ == "__main__":
    # Test the document loader
    loader = DocumentLoader()
    docs = loader.load_documents()
    
    for doc in docs:
        print(f"Document: {doc.metadata['filename']}")
        print(f"Content preview: {doc.content[:200]}...")
        print("-" * 50)
