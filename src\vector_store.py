"""Vector store module using ChromaDB for storing and querying embeddings."""

import os
from typing import List, Dict, <PERSON><PERSON>
import chromadb
from chromadb.config import Settings
import numpy as np
from src.splitter import TextChunk
import config


class VectorStore:
    """ChromaDB-based vector store for document embeddings."""
    
    def __init__(self, db_path: str = None, collection_name: str = None):
        self.db_path = db_path or config.CHROMA_DB_PATH
        self.collection_name = collection_name or config.COLLECTION_NAME
        self.client = None
        self.collection = None
        self._initialize_db()
    
    def _initialize_db(self):
        """Initialize ChromaDB client and collection."""
        print(f"Initializing ChromaDB at: {self.db_path}")
        
        # Create directory if it doesn't exist
        os.makedirs(self.db_path, exist_ok=True)
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=self.db_path,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Get or create collection
        try:
            self.collection = self.client.get_collection(name=self.collection_name)
            print(f"Loaded existing collection: {self.collection_name}")
        except ValueError:
            # Collection doesn't exist, create it
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "RAG document embeddings"}
            )
            print(f"Created new collection: {self.collection_name}")
    
    def add_chunks(self, chunks: List[TextChunk], embeddings: List[np.ndarray]):
        """Add text chunks and their embeddings to the vector store."""
        if len(chunks) != len(embeddings):
            raise ValueError("Number of chunks must match number of embeddings")
        
        if not chunks:
            print("No chunks to add")
            return
        
        print(f"Adding {len(chunks)} chunks to vector store...")
        
        # Prepare data for ChromaDB
        ids = []
        documents = []
        metadatas = []
        embeddings_list = []
        
        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            # Create unique ID
            chunk_id = f"{chunk.metadata.get('filename', 'unknown')}_{i}"
            ids.append(chunk_id)
            
            # Document content
            documents.append(chunk.content)
            
            # Metadata
            metadata = chunk.metadata.copy()
            metadata['chunk_id'] = chunk_id
            metadatas.append(metadata)
            
            # Embedding
            embeddings_list.append(embedding.tolist())
        
        # Add to collection
        self.collection.add(
            ids=ids,
            documents=documents,
            metadatas=metadatas,
            embeddings=embeddings_list
        )
        
        print(f"Successfully added {len(chunks)} chunks to vector store")
    
    def query(self, query_embedding: np.ndarray, top_k: int = None) -> List[Dict]:
        """Query the vector store for similar documents."""
        top_k = top_k or config.TOP_K_RESULTS
        
        # Query the collection
        results = self.collection.query(
            query_embeddings=[query_embedding.tolist()],
            n_results=top_k,
            include=['documents', 'metadatas', 'distances']
        )
        
        # Format results
        formatted_results = []
        for i in range(len(results['ids'][0])):
            result = {
                'id': results['ids'][0][i],
                'document': results['documents'][0][i],
                'metadata': results['metadatas'][0][i],
                'distance': results['distances'][0][i],
                'similarity': 1 - results['distances'][0][i]  # Convert distance to similarity
            }
            formatted_results.append(result)
        
        return formatted_results
    
    def get_collection_info(self) -> Dict:
        """Get information about the collection."""
        count = self.collection.count()
        return {
            'name': self.collection_name,
            'count': count,
            'db_path': self.db_path
        }
    
    def clear_collection(self):
        """Clear all documents from the collection."""
        print(f"Clearing collection: {self.collection_name}")
        
        # Delete the collection and recreate it
        self.client.delete_collection(name=self.collection_name)
        self.collection = self.client.create_collection(
            name=self.collection_name,
            metadata={"description": "RAG document embeddings"}
        )
        
        print("Collection cleared successfully")
    
    def document_exists(self, filename: str) -> bool:
        """Check if documents from a specific file already exist in the collection."""
        try:
            results = self.collection.get(
                where={"filename": filename},
                limit=1
            )
            return len(results['ids']) > 0
        except Exception:
            return False


if __name__ == "__main__":
    # Test the vector store
    from src.loader import DocumentLoader
    from src.splitter import TextSplitter
    from src.embedder import Embedder
    
    # Load, split, and embed documents
    loader = DocumentLoader()
    documents = loader.load_documents()
    
    if documents:
        splitter = TextSplitter()
        chunks = splitter.split_documents(documents)
        
        embedder = Embedder()
        embeddings = embedder.embed_chunks(chunks)
        
        # Test vector store
        vector_store = VectorStore()
        
        # Add chunks to vector store
        vector_store.add_chunks(chunks, embeddings)
        
        # Test query
        query = "What is this document about?"
        query_embedding = embedder.embed_query(query)
        results = vector_store.query(query_embedding, top_k=3)
        
        print(f"\nQuery results for: '{query}'")
        for i, result in enumerate(results):
            print(f"Result {i + 1}:")
            print(f"Similarity: {result['similarity']:.3f}")
            print(f"Document: {result['document'][:100]}...")
            print(f"Metadata: {result['metadata']}")
            print("-" * 50)
        
        # Show collection info
        info = vector_store.get_collection_info()
        print(f"\nCollection info: {info}")
    else:
        print("No documents found to test vector store")
