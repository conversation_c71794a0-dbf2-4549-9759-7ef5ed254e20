"""Question-Answering module that combines retrieval with LLM generation."""

import os
from typing import Dict, List, Optional
import openai
from src.retriever import Retriever
import config


class QAResponse:
    """Represents a QA response with context and metadata."""
    
    def __init__(self, answer: str, context: str, sources: List[str], query: str):
        self.answer = answer
        self.context = context
        self.sources = sources
        self.query = query
    
    def __repr__(self):
        return f"QAResponse(query='{self.query[:50]}...', answer_length={len(self.answer)})"


class QAChain:
    """Retrieval-Augmented Generation chain for question answering."""
    
    def __init__(self, retriever: Retriever = None, model: str = None, prompt_template: str = None):
        self.retriever = retriever or Retriever()
        self.model = model or config.OPENAI_MODEL
        self.prompt_template = prompt_template or config.DEFAULT_PROMPT_TEMPLATE
        
        # Initialize OpenAI client
        self._setup_openai()
    
    def _setup_openai(self):
        """Setup OpenAI client with API key."""
        api_key = config.OPENAI_API_KEY
        
        if not api_key:
            raise ValueError(
                "OpenAI API key not found. Please set OPENAI_API_KEY in your .env file. "
                "Copy .env.template to .env and add your API key."
            )
        
        openai.api_key = api_key
        print(f"OpenAI client initialized with model: {self.model}")
    
    def ask(self, question: str, top_k: int = None, similarity_threshold: float = None) -> QAResponse:
        """Ask a question and get an answer using RAG."""
        print(f"Processing question: '{question}'")
        
        # Retrieve relevant context
        context = self.retriever.retrieve_context(
            question, 
            top_k=top_k, 
            similarity_threshold=similarity_threshold
        )
        
        # Get source information
        retrieval_results = self.retriever.retrieve(
            question, 
            top_k=top_k, 
            similarity_threshold=similarity_threshold
        )
        sources = [result.metadata.get('filename', 'Unknown') for result in retrieval_results]
        sources = list(set(sources))  # Remove duplicates
        
        # Generate answer using LLM
        answer = self._generate_answer(question, context)
        
        return QAResponse(
            answer=answer,
            context=context,
            sources=sources,
            query=question
        )
    
    def _generate_answer(self, question: str, context: str) -> str:
        """Generate an answer using the LLM."""
        # Format the prompt
        prompt = self.prompt_template.format(
            context=context,
            question=question
        )
        
        try:
            # Call OpenAI API
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that answers questions based on the provided context. "
                                 "If the context doesn't contain enough information to answer the question, "
                                 "say so clearly and suggest what additional information might be needed."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            answer = response.choices[0].message.content.strip()
            print("Answer generated successfully")
            return answer
            
        except Exception as e:
            error_msg = f"Error generating answer: {e}"
            print(error_msg)
            return f"Sorry, I encountered an error while generating the answer: {e}"
    
    def batch_ask(self, questions: List[str]) -> List[QAResponse]:
        """Process multiple questions in batch."""
        responses = []
        
        for i, question in enumerate(questions, 1):
            print(f"\nProcessing question {i}/{len(questions)}")
            response = self.ask(question)
            responses.append(response)
        
        return responses
    
    def get_qa_stats(self) -> Dict:
        """Get statistics about the QA system."""
        retriever_stats = self.retriever.get_retriever_stats()
        
        return {
            'model': self.model,
            'retriever_stats': retriever_stats,
            'prompt_template_length': len(self.prompt_template)
        }


class LocalQAChain(QAChain):
    """QA Chain that uses a local model instead of OpenAI."""
    
    def __init__(self, retriever: Retriever = None, local_url: str = None, prompt_template: str = None):
        self.retriever = retriever or Retriever()
        self.local_url = local_url or os.getenv("LOCAL_MODEL_URL")
        self.prompt_template = prompt_template or config.DEFAULT_PROMPT_TEMPLATE
        
        if not self.local_url:
            raise ValueError("Local model URL not provided. Set LOCAL_MODEL_URL in .env file.")
        
        print(f"Local QA Chain initialized with URL: {self.local_url}")
    
    def _setup_openai(self):
        """Override to skip OpenAI setup for local models."""
        pass
    
    def _generate_answer(self, question: str, context: str) -> str:
        """Generate answer using local model (placeholder implementation)."""
        # This is a placeholder - you would implement actual local model calling here
        # For example, using requests to call a local LLM server
        
        return f"[Local Model Response] Based on the context provided, I would answer: {question}"


if __name__ == "__main__":
    # Test the QA chain
    try:
        qa_chain = QAChain()
        
        # Test questions
        test_questions = [
            "What is this document about?",
            "What are the main topics discussed?",
            "Can you summarize the key points?"
        ]
        
        for question in test_questions:
            print(f"\n{'='*60}")
            print(f"Question: {question}")
            print('='*60)
            
            response = qa_chain.ask(question)
            
            print(f"Answer: {response.answer}")
            print(f"Sources: {', '.join(response.sources) if response.sources else 'None'}")
            print(f"Context length: {len(response.context)} characters")
        
        # Show QA stats
        print(f"\n{'='*60}")
        print("QA System Statistics:")
        print('='*60)
        stats = qa_chain.get_qa_stats()
        for key, value in stats.items():
            print(f"{key}: {value}")
            
    except ValueError as e:
        print(f"Configuration error: {e}")
        print("Please make sure you have set up your .env file with the OpenAI API key.")
    except Exception as e:
        print(f"Error testing QA chain: {e}")
