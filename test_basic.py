#!/usr/bin/env python3
"""
Basic test script to verify RAG application components work correctly.
This script tests the core functionality without requiring OpenAI API.
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_document_loader():
    """Test document loading functionality."""
    print("Testing Document Loader...")
    
    from src.loader import DocumentLoader
    
    loader = DocumentLoader()
    documents = loader.load_documents()
    
    assert len(documents) > 0, "No documents loaded"
    assert all(doc.content for doc in documents), "Some documents have empty content"
    
    print(f"✓ Loaded {len(documents)} documents successfully")
    return documents

def test_text_splitter(documents):
    """Test text splitting functionality."""
    print("Testing Text Splitter...")
    
    from src.splitter import TextSplitter
    
    splitter = TextSplitter(chunk_size=200, chunk_overlap=20)
    chunks = splitter.split_documents(documents)
    
    assert len(chunks) > 0, "No chunks created"
    assert all(chunk.content for chunk in chunks), "Some chunks have empty content"
    # Check chunk sizes (allow some flexibility since sentence boundaries matter)
    max_chunk_size = max(len(chunk.content) for chunk in chunks)
    print(f"   Max chunk size: {max_chunk_size} characters")
    assert max_chunk_size <= 1000, f"Chunks too large: max size {max_chunk_size}"
    
    print(f"✓ Created {len(chunks)} chunks successfully")
    return chunks

def test_embedder(chunks):
    """Test embedding generation."""
    print("Testing Embedder...")

    try:
        from src.embedder import Embedder

        # Test with a small subset to avoid long processing time
        test_chunks = chunks[:3] if len(chunks) >= 3 else chunks

        embedder = Embedder()
        embeddings = embedder.embed_chunks(test_chunks)

        assert len(embeddings) == len(test_chunks), "Embedding count mismatch"
        assert all(emb.shape[0] > 0 for emb in embeddings), "Invalid embedding dimensions"

        # Test query embedding
        query_embedding = embedder.embed_query("What is machine learning?")
        assert query_embedding.shape[0] > 0, "Invalid query embedding"

        print(f"✓ Generated {len(embeddings)} embeddings successfully")
        print(f"✓ Embedding dimension: {embedder.get_embedding_dimension()}")
        return embeddings, embedder

    except ImportError as e:
        print(f"⚠ Skipping embedder test - missing dependencies: {e}")
        print("  Run 'pip install -r requirements.txt' to install dependencies")
        # Return mock data for testing
        import random
        mock_embeddings = [[random.random() for _ in range(384)] for _ in range(len(chunks[:3]))]
        return mock_embeddings, None

def test_vector_store(chunks, embeddings):
    """Test vector store functionality."""
    print("Testing Vector Store...")
    
    from src.vector_store import VectorStore
    
    # Use a test collection to avoid interfering with main data
    vector_store = VectorStore(collection_name="test_collection")
    
    # Clear any existing test data
    vector_store.clear_collection()
    
    # Add chunks
    vector_store.add_chunks(chunks[:3], embeddings[:3])
    
    # Test query
    query_embedding = embeddings[0]  # Use first embedding as test query
    results = vector_store.query(query_embedding, top_k=2)
    
    assert len(results) > 0, "No results returned from vector store"
    assert all('similarity' in result for result in results), "Missing similarity scores"
    
    # Check collection info
    info = vector_store.get_collection_info()
    assert info['count'] > 0, "Collection appears empty"
    
    print(f"✓ Vector store working with {info['count']} documents")
    return vector_store

def test_retriever(vector_store, embedder):
    """Test retriever functionality."""
    print("Testing Retriever...")
    
    from src.retriever import Retriever
    
    retriever = Retriever(vector_store=vector_store, embedder=embedder)
    
    # Test retrieval
    results = retriever.retrieve("What is machine learning?", top_k=2)
    
    assert len(results) >= 0, "Retriever failed"
    
    # Test context retrieval
    context = retriever.retrieve_context("What is Python?", top_k=2)
    assert isinstance(context, str), "Context should be a string"
    
    print(f"✓ Retrieved {len(results)} relevant documents")
    return retriever

def main():
    """Run all tests."""
    print("=" * 60)
    print("RAG Application - Basic Component Tests")
    print("=" * 60)
    
    try:
        # Test each component
        documents = test_document_loader()
        chunks = test_text_splitter(documents)
        embeddings, embedder = test_embedder(chunks)
        vector_store = test_vector_store(chunks, embeddings)
        retriever = test_retriever(vector_store, embedder)
        
        print("\n" + "=" * 60)
        print("✓ All tests passed! RAG application components are working correctly.")
        print("=" * 60)
        
        print("\nNext steps:")
        print("1. Copy .env.template to .env")
        print("2. Add your OpenAI API key to .env")
        print("3. Run: python main.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
