"""Text splitter module for splitting documents into overlapping chunks."""

import re
from typing import List, Dict
from src.loader import Document
import config


class TextChunk:
    """Represents a chunk of text with metadata."""
    
    def __init__(self, content: str, metadata: Dict[str, str] = None):
        self.content = content
        self.metadata = metadata or {}
    
    def __repr__(self):
        return f"TextChunk(content_length={len(self.content)}, metadata={self.metadata})"


class TextSplitter:
    """Splits documents into overlapping chunks for better retrieval."""
    
    def __init__(self, chunk_size: int = None, chunk_overlap: int = None):
        self.chunk_size = chunk_size or config.CHUNK_SIZE
        self.chunk_overlap = chunk_overlap or config.CHUNK_OVERLAP
    
    def split_documents(self, documents: List[Document]) -> List[TextChunk]:
        """Split multiple documents into chunks."""
        all_chunks = []
        
        for doc in documents:
            chunks = self.split_document(doc)
            all_chunks.extend(chunks)
        
        print(f"Split {len(documents)} documents into {len(all_chunks)} chunks")
        return all_chunks
    
    def split_document(self, document: Document) -> List[TextChunk]:
        """Split a single document into overlapping chunks."""
        text = document.content
        chunks = []
        
        # Clean and normalize the text
        text = self._clean_text(text)
        
        # Split by sentences first for better chunk boundaries
        sentences = self._split_into_sentences(text)
        
        current_chunk = ""
        current_length = 0
        
        for sentence in sentences:
            sentence_length = len(sentence)

            # If sentence itself is too long, split it further
            if sentence_length > self.chunk_size:
                # If we have a current chunk, finalize it first
                if current_chunk.strip():
                    chunk = self._create_chunk(current_chunk, document, len(chunks))
                    chunks.append(chunk)
                    current_chunk = ""
                    current_length = 0

                # Split the long sentence into smaller parts
                words = sentence.split()
                temp_chunk = ""
                for word in words:
                    if len(temp_chunk + " " + word) > self.chunk_size and temp_chunk:
                        chunk = self._create_chunk(temp_chunk, document, len(chunks))
                        chunks.append(chunk)
                        temp_chunk = word
                    else:
                        if temp_chunk:
                            temp_chunk += " " + word
                        else:
                            temp_chunk = word

                # Add remaining part as current chunk
                current_chunk = temp_chunk
                current_length = len(current_chunk)
                continue

            # If adding this sentence would exceed chunk size, finalize current chunk
            if current_length + sentence_length > self.chunk_size and current_chunk:
                chunk = self._create_chunk(current_chunk, document, len(chunks))
                chunks.append(chunk)

                # Start new chunk with overlap
                current_chunk = self._get_overlap_text(current_chunk)
                current_length = len(current_chunk)

            # Add sentence to current chunk
            if current_chunk and not current_chunk.endswith(' '):
                current_chunk += " "
                current_length += 1

            current_chunk += sentence
            current_length += sentence_length
        
        # Add the final chunk if it has content
        if current_chunk.strip():
            chunk = self._create_chunk(current_chunk, document, len(chunks))
            chunks.append(chunk)
        
        return chunks
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove leading/trailing whitespace
        text = text.strip()
        return text
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences using simple regex."""
        # Simple sentence splitting - can be improved with NLTK
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _get_overlap_text(self, text: str) -> str:
        """Get the overlap text from the end of current chunk."""
        if len(text) <= self.chunk_overlap:
            return text
        
        # Try to find a good breaking point (word boundary)
        overlap_text = text[-self.chunk_overlap:]
        
        # Find the first space to avoid cutting words
        space_index = overlap_text.find(' ')
        if space_index > 0:
            overlap_text = overlap_text[space_index + 1:]
        
        return overlap_text
    
    def _create_chunk(self, content: str, original_doc: Document, chunk_index: int) -> TextChunk:
        """Create a TextChunk with appropriate metadata."""
        metadata = original_doc.metadata.copy()
        metadata.update({
            'chunk_index': chunk_index,
            'chunk_size': len(content),
            'original_doc_size': len(original_doc.content)
        })
        
        return TextChunk(content=content.strip(), metadata=metadata)


if __name__ == "__main__":
    # Test the text splitter
    from src.loader import DocumentLoader
    
    loader = DocumentLoader()
    documents = loader.load_documents()
    
    if documents:
        splitter = TextSplitter()
        chunks = splitter.split_documents(documents)
        
        print(f"\nFirst few chunks:")
        for i, chunk in enumerate(chunks[:3]):
            print(f"Chunk {i + 1}:")
            print(f"Content: {chunk.content[:100]}...")
            print(f"Metadata: {chunk.metadata}")
            print("-" * 50)
