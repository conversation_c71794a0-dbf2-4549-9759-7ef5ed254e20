"""Retriever module for querying the vector store and retrieving relevant documents."""

from typing import List, Dict
from src.vector_store import VectorStore
from src.embedder import Embedder
import config


class RetrievalResult:
    """Represents a single retrieval result."""
    
    def __init__(self, content: str, metadata: Dict, similarity: float):
        self.content = content
        self.metadata = metadata
        self.similarity = similarity
    
    def __repr__(self):
        return f"RetrievalResult(similarity={self.similarity:.3f}, content_length={len(self.content)})"


class Retriever:
    """Handles document retrieval from the vector store."""
    
    def __init__(self, vector_store: VectorStore = None, embedder: Embedder = None):
        self.vector_store = vector_store or VectorStore()
        self.embedder = embedder or Embedder()
        self.top_k = config.TOP_K_RESULTS
        self.similarity_threshold = config.SIMILARITY_THRESHOLD
    
    def retrieve(self, query: str, top_k: int = None, similarity_threshold: float = None) -> List[RetrievalResult]:
        """Retrieve relevant documents for a given query."""
        top_k = top_k or self.top_k
        similarity_threshold = similarity_threshold or self.similarity_threshold
        
        print(f"Retrieving documents for query: '{query[:50]}...'")
        
        # Generate query embedding
        query_embedding = self.embedder.embed_query(query)
        
        # Query vector store
        raw_results = self.vector_store.query(query_embedding, top_k=top_k)
        
        # Filter by similarity threshold and convert to RetrievalResult objects
        filtered_results = []
        for result in raw_results:
            if result['similarity'] >= similarity_threshold:
                retrieval_result = RetrievalResult(
                    content=result['document'],
                    metadata=result['metadata'],
                    similarity=result['similarity']
                )
                filtered_results.append(retrieval_result)
        
        print(f"Retrieved {len(filtered_results)} documents above similarity threshold {similarity_threshold}")
        
        return filtered_results
    
    def retrieve_context(self, query: str, top_k: int = None, similarity_threshold: float = None) -> str:
        """Retrieve relevant documents and format them as context for LLM."""
        results = self.retrieve(query, top_k, similarity_threshold)
        
        if not results:
            return "No relevant documents found."
        
        # Format results as context
        context_parts = []
        for i, result in enumerate(results, 1):
            source_info = f"Source: {result.metadata.get('filename', 'Unknown')}"
            similarity_info = f"Relevance: {result.similarity:.3f}"
            
            context_part = f"Document {i} ({source_info}, {similarity_info}):\n{result.content}"
            context_parts.append(context_part)
        
        return "\n\n" + "\n\n".join(context_parts)
    
    def get_retriever_stats(self) -> Dict:
        """Get statistics about the retriever and vector store."""
        collection_info = self.vector_store.get_collection_info()
        
        return {
            'vector_store_info': collection_info,
            'embedding_model': self.embedder.model_name,
            'embedding_dimension': self.embedder.get_embedding_dimension(),
            'top_k': self.top_k,
            'similarity_threshold': self.similarity_threshold
        }


class HybridRetriever(Retriever):
    """Enhanced retriever that combines semantic search with keyword matching."""
    
    def __init__(self, vector_store: VectorStore = None, embedder: Embedder = None):
        super().__init__(vector_store, embedder)
    
    def retrieve(self, query: str, top_k: int = None, similarity_threshold: float = None) -> List[RetrievalResult]:
        """Retrieve documents using both semantic and keyword matching."""
        # Get semantic results
        semantic_results = super().retrieve(query, top_k, similarity_threshold)
        
        # For now, just return semantic results
        # In a more advanced implementation, you could add keyword-based filtering
        # or re-ranking based on exact keyword matches
        
        return semantic_results


if __name__ == "__main__":
    # Test the retriever
    from src.loader import DocumentLoader
    from src.splitter import TextSplitter
    
    # Check if we have documents in the vector store
    vector_store = VectorStore()
    collection_info = vector_store.get_collection_info()
    
    if collection_info['count'] == 0:
        print("No documents in vector store. Loading and indexing documents...")
        
        # Load and process documents
        loader = DocumentLoader()
        documents = loader.load_documents()
        
        if documents:
            splitter = TextSplitter()
            chunks = splitter.split_documents(documents)
            
            embedder = Embedder()
            embeddings = embedder.embed_chunks(chunks)
            
            vector_store.add_chunks(chunks, embeddings)
            print("Documents indexed successfully!")
        else:
            print("No documents found to index.")
            exit()
    
    # Test retrieval
    retriever = Retriever()
    
    # Test queries
    test_queries = [
        "What is this document about?",
        "Tell me about the main topics",
        "What are the key points?"
    ]
    
    for query in test_queries:
        print(f"\n{'='*60}")
        print(f"Query: {query}")
        print('='*60)
        
        results = retriever.retrieve(query, top_k=3)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"\nResult {i} (Similarity: {result.similarity:.3f}):")
                print(f"Source: {result.metadata.get('filename', 'Unknown')}")
                print(f"Content: {result.content[:200]}...")
        else:
            print("No relevant documents found.")
    
    # Show retriever stats
    print(f"\n{'='*60}")
    print("Retriever Statistics:")
    print('='*60)
    stats = retriever.get_retriever_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")
