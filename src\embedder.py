"""Embedder module for generating embeddings using sentence-transformers."""

from typing import List
import numpy as np
from sentence_transformers import SentenceTransformer
from src.splitter import TextChunk
import config


class Embedder:
    """Handles text embedding using sentence-transformers."""
    
    def __init__(self, model_name: str = None, device: str = None):
        self.model_name = model_name or config.EMBEDDING_MODEL
        self.device = device or config.EMBEDDING_DEVICE
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load the sentence transformer model."""
        print(f"Loading embedding model: {self.model_name}")
        try:
            self.model = SentenceTransformer(self.model_name, device=self.device)
            print(f"Model loaded successfully on device: {self.device}")
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
    
    def embed_chunks(self, chunks: List[TextChunk]) -> List[np.ndarray]:
        """Generate embeddings for a list of text chunks."""
        if not chunks:
            return []
        
        print(f"Generating embeddings for {len(chunks)} chunks...")
        
        # Extract text content from chunks
        texts = [chunk.content for chunk in chunks]
        
        # Generate embeddings
        embeddings = self.model.encode(
            texts,
            show_progress_bar=True,
            convert_to_numpy=True,
            normalize_embeddings=True  # Normalize for cosine similarity
        )
        
        print(f"Generated embeddings with shape: {embeddings.shape}")
        return embeddings
    
    def embed_query(self, query: str) -> np.ndarray:
        """Generate embedding for a single query."""
        embedding = self.model.encode(
            [query],
            convert_to_numpy=True,
            normalize_embeddings=True
        )
        return embedding[0]  # Return single embedding, not array
    
    def embed_text(self, text: str) -> np.ndarray:
        """Generate embedding for a single text."""
        return self.embed_query(text)
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this model."""
        if self.model is None:
            raise ValueError("Model not loaded")
        return self.model.get_sentence_embedding_dimension()


if __name__ == "__main__":
    # Test the embedder
    from src.loader import DocumentLoader
    from src.splitter import TextSplitter
    
    # Load and split documents
    loader = DocumentLoader()
    documents = loader.load_documents()
    
    if documents:
        splitter = TextSplitter()
        chunks = splitter.split_documents(documents)
        
        # Test embedder
        embedder = Embedder()
        
        # Test with a few chunks
        test_chunks = chunks[:3] if len(chunks) >= 3 else chunks
        embeddings = embedder.embed_chunks(test_chunks)
        
        print(f"\nEmbedding results:")
        print(f"Number of embeddings: {len(embeddings)}")
        print(f"Embedding dimension: {embedder.get_embedding_dimension()}")
        
        # Test query embedding
        query = "What is this document about?"
        query_embedding = embedder.embed_query(query)
        print(f"Query embedding shape: {query_embedding.shape}")
    else:
        print("No documents found to test embedder")
