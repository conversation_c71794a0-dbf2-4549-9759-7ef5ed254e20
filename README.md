# RAG Application

A minimal, modular Retrieval-Augmented Generation (RAG) application built in Python. This application allows you to ask questions about your documents using AI-powered semantic search and generation.

## Features

- **Document Loading**: Load plain text files from a `./data/` folder
- **Text Chunking**: Split documents into overlapping chunks for better retrieval
- **Semantic Embeddings**: Use sentence-transformers (all-MiniLM-L6-v2) for embeddings
- **Vector Storage**: Store and query embeddings using ChromaDB with persistence
- **AI-Powered QA**: Answer questions using OpenAI GPT-3.5-turbo with retrieved context
- **CLI Interface**: Interactive command-line interface with single-question mode
- **Docker Support**: Containerized deployment with Docker and docker-compose

## Quick Start

### 1. Setup Environment

```bash
# Clone or download the project
cd rag-application

# Copy environment template
cp .env.template .env

# Edit .env and add your OpenAI API key
# OPENAI_API_KEY=your_openai_api_key_here
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Add Documents

Place your `.txt` files in the `./data/` folder:

```bash
mkdir -p data
# Add your .txt files to the data/ folder
```

### 4. Run the Application

**Interactive Mode:**
```bash
python main.py
```

**Single Question Mode:**
```bash
python main.py --ask "What is this document about?"
```

**Reload Documents:**
```bash
python main.py --reload
```

## Docker Usage

### Build and Run with Docker

```bash
# Build the image
docker build -t rag-app .

# Run interactively
docker run -it --rm \
  -v $(pwd)/data:/app/data:ro \
  -v $(pwd)/chroma_db:/app/chroma_db \
  -v $(pwd)/.env:/app/.env:ro \
  rag-app

# Run single question
docker run --rm \
  -v $(pwd)/data:/app/data:ro \
  -v $(pwd)/chroma_db:/app/chroma_db \
  -v $(pwd)/.env:/app/.env:ro \
  rag-app python main.py --ask "Your question here"
```

### Using Docker Compose

```bash
# Run interactively
docker-compose up

# Run in background
docker-compose up -d

# Stop
docker-compose down
```

## Project Structure

```
rag-application/
├── main.py                 # Main entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── .env.template         # Environment template
├── Dockerfile            # Docker configuration
├── docker-compose.yml    # Docker Compose configuration
├── README.md             # This file
├── data/                 # Document folder (create and add .txt files)
├── chroma_db/           # ChromaDB storage (auto-created)
└── src/                 # Source code modules
    ├── __init__.py
    ├── loader.py        # Document loading
    ├── splitter.py      # Text chunking
    ├── embedder.py      # Embedding generation
    ├── vector_store.py  # ChromaDB interface
    ├── retriever.py     # Document retrieval
    ├── qa.py           # Question-answering chain
    └── ui.py           # CLI interface
```

## Configuration

Edit `config.py` or set environment variables in `.env`:

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_MODEL`: Model to use (default: gpt-3.5-turbo)
- `CHUNK_SIZE`: Text chunk size (default: 500)
- `CHUNK_OVERLAP`: Overlap between chunks (default: 50)
- `TOP_K_RESULTS`: Number of documents to retrieve (default: 5)
- `SIMILARITY_THRESHOLD`: Minimum similarity for retrieval (default: 0.7)

## CLI Commands

In interactive mode, you can use these commands:

- Type any question to get an answer
- `help` - Show available commands
- `stats` - Show system statistics
- `reload` - Reload documents from data folder
- `quit`, `exit`, `q` - Exit the application

## Example Usage

```bash
$ python main.py
==============================================================
RAG Application - Retrieval-Augmented Generation
==============================================================
Initializing RAG system...
Loading documents from data folder...
Loaded: sample_document.txt
Total documents loaded: 1
Split 1 documents into 5 chunks
Loading embedding model: all-MiniLM-L6-v2
Generating embeddings for 5 chunks...
Adding 5 chunks to vector store...
RAG system initialized successfully!

Enter your question: What is this document about?

Processing your question...
Retrieving documents for query: 'What is this document about?'...
Retrieved 3 documents above similarity threshold 0.7
Answer generated successfully

Question: What is this document about?
------------------------------------------------------------
Answer: Based on the provided context, this document appears to be about...

Sources: sample_document.txt
```

## Extending the Application

The modular design makes it easy to extend:

- **Add new file types**: Modify `src/loader.py`
- **Use different embedding models**: Change `EMBEDDING_MODEL` in config
- **Add different LLMs**: Extend `src/qa.py`
- **Implement web interface**: Add Flask/FastAPI on top of existing modules
- **Add more retrieval strategies**: Extend `src/retriever.py`

## Troubleshooting

### Common Issues

1. **No OpenAI API Key**: Make sure you've set `OPENAI_API_KEY` in your `.env` file
2. **No documents found**: Add `.txt` files to the `./data/` folder
3. **ChromaDB errors**: Delete the `chroma_db/` folder to reset the database
4. **Memory issues**: Reduce `CHUNK_SIZE` or process fewer documents at once

### Getting Help

- Check the logs for detailed error messages
- Use `stats` command to see system information
- Verify your `.env` configuration
- Ensure all dependencies are installed correctly

## License

This project is provided as-is for educational and development purposes.
