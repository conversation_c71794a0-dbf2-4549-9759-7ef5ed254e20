version: '3.8'

services:
  rag-app:
    build: .
    container_name: rag-application
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount data directory for documents
      - ./data:/app/data:ro
      # Mount ChromaDB directory for persistence
      - ./chroma_db:/app/chroma_db
      # Mount .env file for configuration
      - ./.env:/app/.env:ro
    stdin_open: true
    tty: true
    # For interactive mode
    command: python main.py
    
    # Uncomment below for single question mode
    # command: python main.py --ask "What is this document about?"
    
    # Uncomment below to reload documents on startup
    # command: python main.py --reload

  # Optional: Add a web interface service in the future
  # rag-web:
  #   build: .
  #   container_name: rag-web
  #   ports:
  #     - "8000:8000"
  #   volumes:
  #     - ./data:/app/data:ro
  #     - ./chroma_db:/app/chroma_db
  #     - ./.env:/app/.env:ro
  #   command: python web_app.py
