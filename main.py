#!/usr/bin/env python3
"""
RAG Application - Main Entry Point

A minimal, modular Retrieval-Augmented Generation (RAG) application.

This application loads documents from a ./data/ folder, creates embeddings using
sentence-transformers, stores them in ChromaDB, and provides a CLI interface
for asking questions that are answered using OpenAI's GPT models with retrieved context.

Usage:
    python main.py                           # Interactive mode
    python main.py --ask "Your question"     # Single question mode
    python main.py --reload                  # Reload documents first
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.ui import main as ui_main


def check_environment():
    """Check if the environment is properly set up."""
    issues = []
    
    # Check if data directory exists
    data_dir = Path("./data")
    if not data_dir.exists():
        issues.append("Data directory './data' does not exist. Creating it now...")
        data_dir.mkdir(exist_ok=True)
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        issues.append(
            ".env file not found. Please copy .env.template to .env and add your OpenAI API key."
        )
    
    # Check if there are any .txt files in data directory
    txt_files = list(data_dir.glob("*.txt"))
    if not txt_files:
        issues.append(
            f"No .txt files found in {data_dir}. Please add some text documents to get started."
        )
    
    return issues


def show_welcome():
    """Show welcome message and basic information."""
    print("=" * 70)
    print("RAG Application - Retrieval-Augmented Generation")
    print("=" * 70)
    print("This application helps you ask questions about your documents using AI.")
    print()
    print("Features:")
    print("• Load documents from ./data/ folder")
    print("• Create semantic embeddings using sentence-transformers")
    print("• Store embeddings in ChromaDB for fast retrieval")
    print("• Answer questions using OpenAI GPT with retrieved context")
    print("• Interactive CLI with command support")
    print()


def show_setup_instructions():
    """Show setup instructions for first-time users."""
    print("Setup Instructions:")
    print("-" * 50)
    print("1. Copy .env.template to .env:")
    print("   cp .env.template .env")
    print()
    print("2. Edit .env and add your OpenAI API key:")
    print("   OPENAI_API_KEY=your_api_key_here")
    print()
    print("3. Add some .txt files to the ./data/ folder")
    print()
    print("4. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("5. Run the application:")
    print("   python main.py")
    print()


def main():
    """Main entry point for the RAG application."""
    show_welcome()
    
    # Check environment setup
    issues = check_environment()
    
    if issues:
        print("Setup Issues Found:")
        print("-" * 50)
        for issue in issues:
            print(f"• {issue}")
        print()
        
        # If critical issues exist, show setup instructions
        if any("API key" in issue or ".env" in issue for issue in issues):
            show_setup_instructions()
            return 1
        
        # If only data directory issues, continue but warn
        if any("txt files" in issue for issue in issues):
            print("Warning: No documents found. You can still run the application,")
            print("but you'll need to add documents to get meaningful answers.")
            print()
    
    # Run the CLI
    try:
        ui_main()
        return 0
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        return 0
    except Exception as e:
        print(f"Error running application: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
