"""Configuration settings for the RAG application."""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# OpenAI Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")

# Embedding Configuration
EMBEDDING_MODEL = "all-MiniLM-L6-v2"
EMBEDDING_DEVICE = "cpu"  # Change to "cuda" if GPU is available

# ChromaDB Configuration
CHROMA_DB_PATH = "./chroma_db"
COLLECTION_NAME = "rag_documents"

# Text Splitting Configuration
CHUNK_SIZE = 500
CHUNK_OVERLAP = 50

# Retrieval Configuration
TOP_K_RESULTS = 5
SIMILARITY_THRESHOLD = 0.7

# Data Configuration
DATA_FOLDER = "./data"
SUPPORTED_EXTENSIONS = [".txt"]

# CLI Configuration
DEFAULT_PROMPT_TEMPLATE = """Based on the following context, please answer the question:

Context:
{context}

Question: {question}

Answer:"""
