"""Command-line interface for the RAG application."""

import argparse
import sys
from typing import Optional
from src.qa import <PERSON><PERSON><PERSON><PERSON>
from src.retriever import Retriever
from src.vector_store import VectorStore
from src.loader import DocumentLoader
from src.splitter import TextSplitter
from src.embedder import Embedder


class RAGCLI:
    """Command-line interface for the RAG application."""
    
    def __init__(self):
        self.qa_chain = None
        self.vector_store = None
        self.is_initialized = False
    
    def initialize(self):
        """Initialize the RAG system components."""
        if self.is_initialized:
            return
        
        print("Initializing RAG system...")
        
        try:
            # Initialize components
            self.vector_store = VectorStore()
            self.qa_chain = QAChain()
            
            # Check if we have documents in the vector store
            collection_info = self.vector_store.get_collection_info()
            
            if collection_info['count'] == 0:
                print("No documents found in vector store. Loading documents...")
                self._load_and_index_documents()
            else:
                print(f"Found {collection_info['count']} documents in vector store.")
            
            self.is_initialized = True
            print("RAG system initialized successfully!")
            
        except Exception as e:
            print(f"Error initializing RAG system: {e}")
            print("Please check your configuration and try again.")
            sys.exit(1)
    
    def _load_and_index_documents(self):
        """Load documents from data folder and index them."""
        print("Loading documents from data folder...")
        
        # Load documents
        loader = DocumentLoader()
        documents = loader.load_documents()
        
        if not documents:
            print("No documents found in the data folder.")
            print("Please add some .txt files to the ./data/ folder and try again.")
            sys.exit(1)
        
        # Split documents into chunks
        print("Splitting documents into chunks...")
        splitter = TextSplitter()
        chunks = splitter.split_documents(documents)
        
        # Generate embeddings
        print("Generating embeddings...")
        embedder = Embedder()
        embeddings = embedder.embed_chunks(chunks)
        
        # Add to vector store
        print("Adding chunks to vector store...")
        self.vector_store.add_chunks(chunks, embeddings)
        
        print(f"Successfully indexed {len(chunks)} chunks from {len(documents)} documents.")
    
    def ask_question(self, question: str) -> str:
        """Ask a question and return the answer."""
        if not self.is_initialized:
            self.initialize()
        
        try:
            response = self.qa_chain.ask(question)
            return self._format_response(response)
        except Exception as e:
            return f"Error processing question: {e}"
    
    def _format_response(self, response) -> str:
        """Format the QA response for display."""
        output = []
        output.append(f"Question: {response.query}")
        output.append("-" * 60)
        output.append(f"Answer: {response.answer}")
        
        if response.sources:
            output.append(f"\nSources: {', '.join(response.sources)}")
        
        return "\n".join(output)
    
    def interactive_mode(self):
        """Run the CLI in interactive mode."""
        print("=" * 60)
        print("RAG Application - Interactive Mode")
        print("=" * 60)
        print("Type 'quit', 'exit', or 'q' to exit")
        print("Type 'help' for available commands")
        print("Type 'stats' to see system statistics")
        print("Type 'reload' to reload documents")
        print("-" * 60)
        
        self.initialize()
        
        while True:
            try:
                question = input("\nEnter your question: ").strip()
                
                if not question:
                    continue
                
                # Handle special commands
                if question.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                elif question.lower() == 'help':
                    self._show_help()
                    continue
                elif question.lower() == 'stats':
                    self._show_stats()
                    continue
                elif question.lower() == 'reload':
                    self._reload_documents()
                    continue
                
                # Process the question
                print("\nProcessing your question...")
                answer = self.ask_question(question)
                print(f"\n{answer}")
                
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"\nError: {e}")
    
    def _show_help(self):
        """Show help information."""
        help_text = """
Available commands:
- Type any question to get an answer
- 'help' - Show this help message
- 'stats' - Show system statistics
- 'reload' - Reload documents from data folder
- 'quit', 'exit', 'q' - Exit the application

Example questions:
- "What is this document about?"
- "Summarize the main points"
- "Tell me about [specific topic]"
        """
        print(help_text)
    
    def _show_stats(self):
        """Show system statistics."""
        if not self.is_initialized:
            print("System not initialized yet.")
            return
        
        try:
            stats = self.qa_chain.get_qa_stats()
            collection_info = self.vector_store.get_collection_info()
            
            print("\nSystem Statistics:")
            print("-" * 40)
            print(f"Documents in vector store: {collection_info['count']}")
            print(f"Collection name: {collection_info['name']}")
            print(f"Database path: {collection_info['db_path']}")
            print(f"LLM model: {stats['model']}")
            print(f"Embedding model: {stats['retriever_stats']['embedding_model']}")
            print(f"Embedding dimension: {stats['retriever_stats']['embedding_dimension']}")
            print(f"Top-K results: {stats['retriever_stats']['top_k']}")
            print(f"Similarity threshold: {stats['retriever_stats']['similarity_threshold']}")
            
        except Exception as e:
            print(f"Error getting statistics: {e}")
    
    def _reload_documents(self):
        """Reload documents from the data folder."""
        print("Reloading documents...")
        
        try:
            # Clear existing collection
            self.vector_store.clear_collection()
            
            # Reload and index documents
            self._load_and_index_documents()
            
            print("Documents reloaded successfully!")
            
        except Exception as e:
            print(f"Error reloading documents: {e}")


def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(
        description="RAG Application - Retrieval-Augmented Generation CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # Interactive mode
  python main.py --ask "What is this about?"  # Single question mode
        """
    )
    
    parser.add_argument(
        "--ask",
        type=str,
        help="Ask a single question and exit"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Reload documents before processing"
    )
    
    args = parser.parse_args()
    
    # Initialize CLI
    cli = RAGCLI()
    
    # Handle reload flag
    if args.reload:
        print("Reloading documents...")
        cli.initialize()
        cli._reload_documents()
    
    # Handle single question mode
    if args.ask:
        answer = cli.ask_question(args.ask)
        print(answer)
        return
    
    # Run interactive mode
    cli.interactive_mode()


if __name__ == "__main__":
    main()
